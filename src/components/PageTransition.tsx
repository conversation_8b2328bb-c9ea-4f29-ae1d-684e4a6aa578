import { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';

interface PageTransitionProps {
  children: React.ReactNode;
}

const PageTransition = ({ children }: PageTransitionProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [showContent, setShowContent] = useState(true);
  const location = useLocation();

  useEffect(() => {
    // Start loading when location changes
    setIsLoading(true);
    setShowContent(false);

    // Optimal loading duration for UX (800ms - enough feedback without feeling slow)
    const timer = setTimeout(() => {
      setIsLoading(false);
      setShowContent(true);
    }, 800);

    return () => clearTimeout(timer);
  }, [location.pathname]);

  if (isLoading) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-gradient-to-br from-neutral via-white to-tertiary/10">
        {/* Loading Content */}
        <div className="flex flex-col items-center space-y-6 animate-fade-in">
          {/* Logo with pulse animation */}
          <div className="relative">
            <img
              src="/logos/logo0.png"
              alt="Amara Nursing LLC"
              className="h-16 w-auto object-contain animate-pulse"
            />
          </div>

          {/* Elegant Spinner */}
          <div className="relative">
            <div className="w-8 h-8 border-2 border-tertiary/30 rounded-full animate-spin">
              <div className="absolute top-0 left-0 w-8 h-8 border-2 border-transparent border-t-accent rounded-full animate-spin"></div>
            </div>
          </div>

          {/* Loading Text */}
          <div className="text-center space-y-2">
            <p className="text-primary font-semibold text-lg">Loading Page...</p>
            <p className="text-text-light text-sm max-w-xs">Please wait while we prepare your content</p>
          </div>

          {/* Progress dots */}
          <div className="flex space-x-2">
            <div className="w-2 h-2 bg-accent rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-accent rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-2 h-2 bg-accent rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          </div>
        </div>

        {/* Subtle background elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute -top-4 -left-4 w-24 h-24 bg-tertiary/10 rounded-full animate-float"></div>
          <div className="absolute top-1/4 -right-8 w-32 h-32 bg-accent/5 rounded-full animate-float-delayed"></div>
          <div className="absolute -bottom-8 left-1/3 w-20 h-20 bg-primary/5 rounded-full animate-float"></div>
        </div>
      </div>
    );
  }

  return (
    <div className={`transition-opacity duration-300 ${showContent ? 'opacity-100' : 'opacity-0'}`}>
      {children}
    </div>
  );
};

export default PageTransition;
