import { Link } from 'react-router-dom';
import Logo from './Logo';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-primary text-white">
      <div className="container mx-auto px-6 py-16">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-12 lg:gap-16">

          {/* Logo and Tagline - Takes more space on desktop for visual balance */}
          <div className="lg:col-span-5 text-center lg:text-left">
            <div className="mb-6">
              <Logo className="h-16 sm:h-18 lg:h-20 w-auto brightness-0 invert mx-auto lg:mx-0" />
            </div>
            <p className="text-base lg:text-lg text-tertiary/90 leading-relaxed max-w-md mx-auto lg:mx-0">
              Compassionate care, expertly delivered in Seattle and surrounding areas.
            </p>
          </div>

          {/* Quick Links - Compact but touch-friendly */}
          <div className="lg:col-span-3 text-center lg:text-left">
            <h3 className="text-xl font-bold text-white mb-6 tracking-wide">Quick Links</h3>
            <nav>
              <ul className="space-y-4">
                <li>
                  <Link
                    to="/about"
                    className="text-base text-tertiary/90 hover:text-white transition-colors duration-300 inline-block py-1 px-2 -mx-2 rounded hover:bg-white/5"
                  >
                    About Us
                  </Link>
                </li>
                <li>
                  <Link
                    to="/contact"
                    className="text-base text-tertiary/90 hover:text-white transition-colors duration-300 inline-block py-1 px-2 -mx-2 rounded hover:bg-white/5"
                  >
                    Contact
                  </Link>
                </li>
              </ul>
            </nav>
          </div>

          {/* Contact Info - Streamlined for mobile */}
          <div className="lg:col-span-4 text-center lg:text-left">
            <h3 className="text-xl font-bold text-white mb-6 tracking-wide">Contact</h3>
            <address className="not-italic text-base text-tertiary/90 space-y-6">

              {/* Adult Family Home */}
              <div className="space-y-2">
                <p className="text-white font-semibold text-sm uppercase tracking-wider">Adult Family Home</p>
                <p className="leading-relaxed">
                  17705 54th Avenue W<br />
                  Lynnwood, WA 98037
                </p>
                <a
                  href="tel:+18174719954"
                  className="text-tertiary hover:text-white transition-colors duration-300 font-medium inline-block py-1 px-2 -mx-2 rounded hover:bg-white/5"
                >
                  (*************
                </a>
              </div>

              {/* Administrative Office */}
              <div className="space-y-2">
                <p className="text-white font-semibold text-sm uppercase tracking-wider">Administrative Office</p>
                <p className="leading-relaxed">
                  820 Lenora St, Unit #1105<br />
                  Seattle, WA 98121
                </p>
                <a
                  href="tel:+18176001793"
                  className="text-tertiary hover:text-white transition-colors duration-300 font-medium inline-block py-1 px-2 -mx-2 rounded hover:bg-white/5"
                >
                  (*************
                </a>
              </div>

            </address>
          </div>
        </div>

        {/* Bottom Credits - Clean separator */}
        <div className="border-t border-tertiary/20 mt-16 pt-8">
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4 text-sm text-tertiary/70">
            <p className="text-center sm:text-left">
              &copy; {currentYear} Amara Nursing LLC. All rights reserved.
            </p>
            <p className="text-center sm:text-right">
              powered by <a
                href="https://atomio.tech"
                target="_blank"
                rel="noopener noreferrer"
                className="text-white hover:text-accent transition-colors duration-300 font-medium"
              >
                atomio
              </a>
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
