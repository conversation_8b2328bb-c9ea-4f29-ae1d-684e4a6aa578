import { Helmet } from 'react-helmet-async';
import { Mail, MapPin } from 'lucide-react';
import ContactForm from '../components/ContactForm';
import GoogleMap from '../components/GoogleMap';

const Contact = () => {
  return (
    <>
      <Helmet>
        <title>Contact Amara Nursing LLC | Seattle In-Home Care | (*************</title>
        <meta name="description" content="Contact Amara Nursing LLC for professional in-home nursing services and adult family home care in Seattle, Lynnwood, Everett, WA. Call (************* <NAME_EMAIL> for expert RN-led care consultation." />
        <meta name="keywords" content="contact Amara Nursing, Seattle nursing services phone, Lynnwood adult family home contact, nursing care consultation, RN-led care Seattle, home health care contact, skilled nursing services phone number" />
        <link rel="canonical" href="https://amaracares.com/contact" />
        <meta property="og:title" content="Contact Amara Nursing LLC | Seattle In-Home Care | (*************" />
        <meta property="og:description" content="Contact Amara Nursing LLC for professional in-home nursing services and adult family home care in Seattle, Lynnwood, Everett, WA. Expert RN-led care consultation." />
        <meta property="og:url" content="https://amaracares.com/contact" />
        <meta property="og:type" content="website" />
      </Helmet>

      {/* Contact Information & Form Section */}
      <section className="bg-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-5xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
              {/* Contact Form */}
              <div>
                <h2 className="text-2xl font-bold text-primary mb-6">Send Us a Message</h2>
                <ContactForm />
              </div>
              
              {/* Contact Information */}
              <div>
                <h2 className="text-2xl font-bold text-primary mb-6">Get in Touch</h2>
                
                <div className="space-y-8">
                  {/* Office Location */}
                  <div className="flex items-start">
                    <div className="bg-tertiary/20 p-3 rounded-full mr-4">
                      <MapPin className="w-6 h-6 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-bold text-lg mb-2">Administrative Office</h3>
                      <address className="not-italic text-text-light">
                        820 Lenora St, Unit #1105<br />
                        Seattle, WA 98121<br />
                        <span className="text-primary font-medium">Tel: </span>
                        <a href="tel:+18176001793" className="hover:text-accent transition-colors">
                          (*************
                        </a>
                      </address>
                    </div>
                  </div>

                  {/* Adult Family Home Location */}
                  <div className="flex items-start">
                    <div className="bg-tertiary/20 p-3 rounded-full mr-4">
                      <MapPin className="w-6 h-6 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-bold text-lg mb-2">Adult Family Home</h3>
                      <address className="not-italic text-text-light">
                        17705 54th Avenue W<br />
                        Lynnwood, WA 98037<br />
                        <span className="text-primary font-medium">Tel: </span>
                        <a href="tel:+18174719954" className="hover:text-accent transition-colors">
                          (*************
                        </a>
                      </address>
                    </div>
                  </div>
                  
                  {/* Email */}
                  <div className="flex items-start">
                    <div className="bg-tertiary/20 p-3 rounded-full mr-4">
                      <Mail className="w-6 h-6 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-bold text-lg mb-2">Email</h3>
                      <p className="text-text-light">
                        <a href="mailto:<EMAIL>" className="hover:text-accent transition-colors">
                          <EMAIL>
                        </a>
                      </p>
                    </div>
                  </div>
                </div>
                
                {/* Business Hours */}
                <div className="mt-12 p-6 bg-tertiary/10 rounded-lg">
                  <h3 className="font-bold text-lg mb-4 text-primary">Business Hours</h3>
                  <ul className="space-y-2 text-text-light">
                    <li className="flex justify-between">
                      <span>Monday - Friday:</span>
                      <span>8:00 AM - 6:00 PM</span>
                    </li>
                    <li className="flex justify-between">
                      <span>Saturday:</span>
                      <span>9:00 AM - 3:00 PM</span>
                    </li>
                    <li className="flex justify-between">
                      <span>Sunday:</span>
                      <span>Closed</span>
                    </li>
                  </ul>
                  <p className="mt-4 text-sm text-text-light italic">
                    Note: Adult Family Home visitation hours may differ. Please call ahead to schedule a visit.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Map Section */}
      <section className="bg-tertiary/10 py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-5xl mx-auto text-center mb-10">
            <h2 className="text-3xl font-bold mb-4">Our Locations</h2>
            <p className="text-text-light">
              Serving Seattle and surrounding areas with compassionate, professional care.
            </p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-sm">
            {/* Google Maps integration */}
            <div className="rounded-lg h-96 overflow-hidden">
              <GoogleMap 
                address="17705 54th Avenue W, Lynnwood, WA 98037"
                height="100%"
              />
              <p className="text-text-light mt-4 max-w-md text-center mx-auto">
                Our services are available throughout the greater Seattle area, with our Adult Family Home located in Lynnwood.
              </p>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default Contact;
