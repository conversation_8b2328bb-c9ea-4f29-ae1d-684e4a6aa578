import { Link } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { ArrowR<PERSON> } from 'lucide-react';
import TestimonialSlider from '../components/TestimonialSlider';

const Home = () => {
  return (
    <>
      <Helmet>
        <title>Amara Nursing LLC | Expert In-Home Nursing & Adult Family Home | Seattle, WA</title>
        <meta name="description" content="Professional RN-led in-home nursing care and licensed Adult Family Home in Seattle, Lynnwood, Everett, WA. Specialized in medically complex care, post-hospitalization recovery, and 24/7 nursing services. Skilled Nursing, Delivered with Heart." />
        <meta name="keywords" content="in-home nursing Seattle, skilled nursing care Washington, adult family home Lynnwood, RN-led care, home health care Seattle, private duty nursing, post-hospitalization care, medically complex care, King County nursing, Snohomish County nursing, professional nursing services" />
        <link rel="canonical" href="https://amaracares.com" />
        <meta property="og:title" content="Amara Nursing LLC | Expert In-Home Nursing & Adult Family Home | Seattle, WA" />
        <meta property="og:description" content="Professional RN-led in-home nursing care and licensed Adult Family Home in Seattle, Lynnwood, Everett, WA. Specialized in medically complex care and 24/7 nursing services." />
        <meta property="og:url" content="https://amaracares.com" />
        <meta property="og:type" content="website" />
      </Helmet>

      {/* Hero Section */}
      <section className="relative overflow-hidden min-h-[600px] flex items-center">
        {/* Background Image */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: 'url(/images/webp/hero-image.webp)',
          }}
        >
          {/* Overlay for better text readability */}
          <div className="absolute inset-0 bg-black/40"></div>
        </div>

        <div className="container mx-auto px-4 py-16 md:py-24 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-4 leading-tight">
              Compassionate Care, Expertly Delivered.
            </h1>
            <p className="text-lg text-white/90 mb-8 max-w-2xl mx-auto">
              Amara Nursing LLC provides professional in-home nursing care and a dedicated Adult Family Home in Lynnwood, WA.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/services" className="btn-primary">
                Explore Our Services
              </Link>
              <Link to="/afh" className="btn bg-white text-primary hover:bg-white/90">
                Learn About Our AFH
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Who We Are Section */}
      <section className="bg-white py-16">
        <div className="container mx-auto px-4 text-center max-w-3xl">
          <h2 className="text-3xl font-bold mb-6">Your Trusted Partners in Health.</h2>
          <p className="text-lg text-text-light mb-8">
            Led by experienced advanced Registered Nurses, we are committed to providing personalized, high-quality nursing care. We believe in dignity, comfort, and peace of mind for every individual we serve.
          </p>
          <div className="flex justify-center gap-8 mt-8">
            <div className="flex flex-col items-center">
              <div className="w-16 h-16 bg-tertiary/30 rounded-full flex items-center justify-center mb-3">
                <svg className="w-8 h-8 text-primary" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm0-2a6 6 0 100-12 6 6 0 000 12z" clipRule="evenodd" />
                </svg>
              </div>
              <span className="font-semibold text-sm">Experienced RNs</span>
            </div>
            <div className="flex flex-col items-center">
              <div className="w-16 h-16 bg-tertiary/30 rounded-full flex items-center justify-center mb-3">
                <svg className="w-8 h-8 text-primary" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <span className="font-semibold text-sm">Quality Care</span>
            </div>
            <div className="flex flex-col items-center">
              <div className="w-16 h-16 bg-tertiary/30 rounded-full flex items-center justify-center mb-3">
                <svg className="w-8 h-8 text-primary" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z" />
                </svg>
              </div>
              <span className="font-semibold text-sm">Personalized Approach</span>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="bg-tertiary/10 py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Our Services</h2>
            <p className="text-lg text-text-light">How We Can Help</p>
          </div>
          <div className="max-w-4xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Service Card 1 */}
            <div className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow p-6 flex flex-col h-full">
              <div className="bg-primary/10 w-12 h-12 rounded-full flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-3">In-Home Nursing</h3>
              <p className="text-text-light mb-4 flex-grow">
                Skilled nursing, private duty, and post-hospitalization recovery in the comfort of your home.
              </p>
              <Link to="/services" className="text-primary font-semibold flex items-center gap-1 hover:text-accent transition-colors">
                Learn More <ArrowRight size={16} />
              </Link>
            </div>
            
            {/* Service Card 2 */}
            <div className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow p-6 flex flex-col h-full">
              <div className="bg-primary/10 w-12 h-12 rounded-full flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-3">Adult Family Home</h3>
              <p className="text-text-light mb-4 flex-grow">
                Medically intensive care in a peaceful, home-like environment in Lynnwood, WA.
              </p>
              <Link to="/afh" className="text-primary font-semibold flex items-center gap-1 hover:text-accent transition-colors">
                Discover Our AFH <ArrowRight size={16} />
              </Link>
            </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonial Section */}
      <section className="bg-white py-16">
        <div className="container mx-auto px-4">
          <TestimonialSlider />
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="bg-tertiary/10 py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold text-primary mb-4">Ready for Dedicated Care?</h2>
          <p className="text-lg text-text-light mb-8 max-w-lg mx-auto">
            Skilled Nursing, Delivered with Heart. Let's discuss your needs.
          </p>
          <Link to="/contact" className="btn-primary">
            Contact Us Today
          </Link>
        </div>
      </section>
    </>
  );
};

export default Home;
