import { Helmet } from 'react-helmet-async';
import { <PERSON> } from 'react-router-dom';
import { Award, Compass, Heart, Users } from 'lucide-react';
import { useState } from 'react';
import TeamModal from '../components/TeamModal';
import { Leader, teamMembers } from '../data/index';



const About = () => {
  const [selectedLeader, setSelectedLeader] = useState<Leader | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openModal = (leader: Leader) => {
    setSelectedLeader(leader);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedLeader(null);
  };

  return (
    <>
      <Helmet>
        <title>About Amara Nursing LLC | Expert RN-Led Care Team | Seattle, WA</title>
        <meta name="description" content="Meet the expert nursing team at Amara Nursing LLC. Licensed RNs <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> provide professional in-home and adult family home care in Seattle, Washington. HIPAA-compliant, compassionate healthcare." />
        <meta name="keywords" content="Amara Nursing LLC team, <PERSON>, <PERSON>, <PERSON>, nursing team Seattle, licensed registered nurses Washington, RN-led care, professional nursing staff, HIPAA compliant nursing, adult family home staff" />
        <link rel="canonical" href="https://amaracares.com/about" />
        <meta property="og:title" content="About Amara Nursing LLC | Expert RN-Led Care Team | Seattle, WA" />
        <meta property="og:description" content="Meet the expert nursing team at Amara Nursing LLC. Licensed RNs and PMHNP providing professional in-home and adult family home care in Seattle, Washington." />
        <meta property="og:url" content="https://amaracares.com/about" />
        <meta property="og:type" content="website" />
      </Helmet>

      {/* Hero Section */}
      <section className="bg-neutral relative overflow-hidden">
        <div className="container mx-auto px-4 py-16 md:py-24">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-primary mb-6 leading-tight">
              About Amara Nursing
            </h1>
            <p className="text-lg text-text-light mb-8">
              Our story, our mission, and the passionate team behind our commitment to exceptional healthcare.
            </p>
          </div>
        </div>
        {/* Abstract background element */}
        <div className="absolute top-0 right-0 -z-10 w-1/3 h-full bg-tertiary/30 rounded-bl-[100px] opacity-50"></div>
      </section>



      {/* Our Mission & Values Section */}
      <section className="bg-tertiary/10 py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Our Mission & Values</h2>
            <p className="text-text-light">
              At the heart of everything we do is a commitment to enhancing the quality of life for those we serve.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            <div className="bg-white p-6 rounded-lg shadow-sm flex flex-col items-center text-center">
              <div className="bg-primary/10 p-3 rounded-full mb-4">
                <Compass className="w-6 h-6 text-primary" />
              </div>
              <h3 className="text-xl font-bold mb-2">Our Mission</h3>
              <p className="text-text-light">
                To deliver exceptional nursing care that empowers individuals to maintain dignity, independence, and well-being in the setting that best suits their needs.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm flex flex-col items-center text-center">
              <div className="bg-primary/10 p-3 rounded-full mb-4">
                <Heart className="w-6 h-6 text-primary" />
              </div>
              <h3 className="text-xl font-bold mb-2">Compassion</h3>
              <p className="text-text-light">
                We approach each individual with empathy, respect, and genuine care, recognizing that healthcare is deeply personal and emotional.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm flex flex-col items-center text-center">
              <div className="bg-primary/10 p-3 rounded-full mb-4">
                <Award className="w-6 h-6 text-primary" />
              </div>
              <h3 className="text-xl font-bold mb-2">Excellence</h3>
              <p className="text-text-light">
                We maintain the highest standards of clinical expertise, continuously improving our skills to provide innovative, evidence-based care.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Leadership Team Section */}
      <section className="bg-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Our Leadership Team</h2>
            <p className="text-text-light">
              Meet the experienced healthcare professionals guiding our organization.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
            {teamMembers.map((leader) => (
              <div key={leader.id} className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
                {/* Leader Image */}
                <div className="relative h-56 bg-primary/10 overflow-hidden">
                  {leader.image && !leader.image.includes('placeholder') ? (
                    <img
                      src={leader.image}
                      alt={leader.name}
                      className="w-full h-full object-cover object-top"
                      onError={(e) => {
                        // Fallback to placeholder if image fails to load
                        e.currentTarget.style.display = 'none';
                        const placeholder = e.currentTarget.parentElement?.querySelector('.placeholder-content');
                        if (placeholder) {
                          (placeholder as HTMLElement).style.display = 'flex';
                        }
                      }}
                    />
                  ) : (
                    /* Show placeholder only when no image is available */
                    <div className="placeholder-content absolute inset-0 flex items-center justify-center bg-primary/10">
                      <Users className="w-16 h-16 text-primary opacity-50" />
                      <p className="text-primary ml-3 text-sm">Leader Photo</p>
                    </div>
                  )}
                </div>

                {/* Leader Info */}
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-2 text-primary">{leader.name}</h3>
                  <p className="text-accent font-medium mb-4">{leader.title}</p>
                  <button
                    onClick={() => openModal(leader)}
                    className="text-primary font-semibold hover:text-accent transition-colors text-sm"
                  >
                    Learn More →
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Qualifications & Certifications Section */}
      {/*
      <section className="bg-tertiary/10 py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4">Qualifications & Approach</h2>
              <p className="text-text-light">
                Our work is guided by industry best practices and continuous education.
              </p>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-sm mb-8">
              <div className="flex items-start gap-4 mb-6">
                <div className="bg-primary/10 p-3 rounded-full">
                  <Sparkles className="w-6 h-6 text-primary" />
                </div>
                <div>
                  <h3 className="text-xl font-bold mb-2">Our Credentials</h3>
                  <ul className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <li className="flex items-start">
                      <div className="text-accent mr-2">•</div>
                      <span className="text-text-light">Licensed Registered Nurses</span>
                    </li>
                    <li className="flex items-start">
                      <div className="text-accent mr-2">•</div>
                      <span className="text-text-light">HIPAA Certified</span>
                    </li>
                    <li className="flex items-start">
                      <div className="text-accent mr-2">•</div>
                      <span className="text-text-light">Adult Family Home License</span>
                    </li>
                    <li className="flex items-start">
                      <div className="text-accent mr-2">•</div>
                      <span className="text-text-light">CPR & First Aid Certified</span>
                    </li>
                    <li className="flex items-start">
                      <div className="text-accent mr-2">•</div>
                      <span className="text-text-light">Nurse Delegation Certified</span>
                    </li>
                    <li className="flex items-start">
                      <div className="text-accent mr-2">•</div>
                      <span className="text-text-light">Dementia Care Specialists</span>
                    </li>
                  </ul>
                </div>
              </div>

              <p className="text-text-light mb-4">
                Our team maintains ongoing education in specialized areas of healthcare, ensuring we remain at the forefront of nursing best practices and person-centered care approaches.
              </p>

              <p className="text-text-light">
                Amara Nursing is fully insured and compliant with all state and federal regulations governing healthcare services and Adult Family Homes.
              </p>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-sm">
              <h3 className="text-xl font-bold mb-4">Our Care Philosophy</h3>
              <p className="text-text-light mb-4">
                We believe in treating the whole person, not just their medical conditions. Our approach integrates physical care with emotional support, recognizing that true well-being encompasses both.
              </p>
              <p className="text-text-light mb-4">
                By developing relationships built on trust and understanding, we create care plans that honor individual preferences, cultural backgrounds, and personal goals. We collaborate closely with clients, families, and other healthcare providers to ensure seamless, coordinated care.
              </p>
              <p className="text-text-light">
                Most importantly, we recognize that inviting caregivers into your home or choosing a residential care setting is a significant decision. We approach this privilege with the utmost respect and commitment to excellence.
              </p>
            </div>
          </div>
        </div>
      </section>
      */}

      {/* HIPAA Compliance Section */}
      {/*
      <section className="bg-tertiary/10 py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto flex flex-col md:flex-row items-center gap-8">
            <div className="bg-white p-4 rounded-full">
              <Shield className="w-16 h-16 text-primary" />
            </div>
            <div>
              <h2 className="text-2xl font-bold mb-3">HIPAA Compliant Care</h2>
              <p className="text-text-light mb-4">
                Your privacy and confidentiality are paramount. All Amara Nursing services strictly adhere to HIPAA regulations, ensuring your personal health information remains protected at all times.
              </p>
              <p className="text-text-light">
                Our nursing team undergoes regular training in privacy practices and security protocols to maintain the highest standards of confidentiality in healthcare delivery.
              </p>
            </div>
          </div>
        </div>
      </section>
      */}

      {/* Call to Action */}
      <section className="bg-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4 text-primary">Ready to Connect?</h2>
          <p className="text-lg text-text-light mb-8 max-w-2xl mx-auto">
            We'd love to discuss how our services can support you or your loved one's healthcare needs.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link to="/contact" className="btn-primary">
              Contact Us
            </Link>
            <Link to="/services" className="btn bg-transparent border-2 border-primary text-primary hover:bg-primary/10 transition-colors">
              Explore Our Services
            </Link>
          </div>
        </div>
      </section>

      {/* Leader Modal */}
      <TeamModal
        isOpen={isModalOpen}
        onClose={closeModal}
        leader={selectedLeader}
      />
    </>
  );
};

export default About;
