import { Helmet } from 'react-helmet-async';
import { <PERSON> } from 'react-router-dom';
import { Clock, Heart, Leaf, Shield } from 'lucide-react';
import GoogleMap from '../components/GoogleMap';

const AdultFamilyHome = () => {
  return (
    <>
      <Helmet>
        <title>Adult Family Home Lynnwood WA | 24/7 Nursing Care | Amara Nursing LLC</title>
        <meta name="description" content="Licensed Adult Family Home in Lynnwood, WA offering 24/7 professional nursing care for medically complex adults. RN-led care in a comfortable, home-like setting. Serving Seattle metro area with specialized residential care services." />
        <meta name="keywords" content="adult family home Lynnwood, residential nursing care Washington, 24/7 nursing care, medically complex care facility, licensed adult family home Seattle, RN-led residential care, home-like nursing facility, Snohomish County adult care" />
        <link rel="canonical" href="https://amaracares.com/afh" />
        <meta property="og:title" content="Adult Family Home Lynnwood WA | 24/7 Nursing Care | Amara Nursing LLC" />
        <meta property="og:description" content="Licensed Adult Family Home in Lynnwood, WA offering 24/7 professional nursing care for medically complex adults. RN-led care in a comfortable, home-like setting." />
        <meta property="og:url" content="https://amaracares.com/afh" />
        <meta property="og:type" content="website" />
      </Helmet>

      {/* Hero Section */}
      <section className="relative overflow-hidden min-h-[600px] flex items-center">
        {/* Background Image */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: 'url(/images/webp/afh.webp)',
          }}
        >
          {/* Overlay for better text readability */}
          <div className="absolute inset-0 bg-black/40"></div>
        </div>

        <div className="container mx-auto px-4 py-16 md:py-24 relative z-10">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6 leading-tight">
              Adult Family House
            </h1>
            <p className="text-lg text-white/90 mb-8 max-w-2xl mx-auto">
              Our Adult Family House in Lynnwood provides a comfortable, supportive environment with professional nursing care for adults needing assistance with daily living.
            </p>
            <Link to="/contact" className="btn-primary">
              Schedule a Tour
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="bg-tertiary/10 py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Features & Amenities</h2>
            <p className="text-text-light">
              Our comfortable, well-equipped home is designed to provide both safety and a pleasant living experience.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            <div className="bg-white p-6 rounded-lg shadow-sm flex flex-col items-center text-center">
              <div className="bg-primary/10 p-3 rounded-full mb-4">
                <Shield className="w-6 h-6 text-primary" />
              </div>
              <h3 className="text-xl font-bold mb-2">24/7 Care</h3>
              <p className="text-text-light">
                Professional caregivers supervised by Registered Nurses available around the clock for assistance and monitoring.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm flex flex-col items-center text-center">
              <div className="bg-primary/10 p-3 rounded-full mb-4">
                <Heart className="w-6 h-6 text-primary" />
              </div>
              <h3 className="text-xl font-bold mb-2">Medical Support</h3>
              <p className="text-text-light">
                Medication management, vital sign monitoring, wound care, and coordination with physicians and specialists.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm flex flex-col items-center text-center">
              <div className="bg-primary/10 p-3 rounded-full mb-4">
                <Leaf className="w-6 h-6 text-primary" />
              </div>
              <h3 className="text-xl font-bold mb-2">Calm Environment</h3>
              <p className="text-text-light">
                Peaceful residential setting with private and semi-private rooms, comfortable common areas, and accessible gardens.
              </p>
            </div>
          </div>

          <div className="mt-12 grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <h3 className="text-xl font-bold mb-3">Our Services Include:</h3>
              <ul className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <li className="flex items-start">
                  <div className="text-accent mr-2">•</div>
                  <span className="text-text-light">Personal care assistance</span>
                </li>
                <li className="flex items-start">
                  <div className="text-accent mr-2">•</div>
                  <span className="text-text-light">Nutritious, home-cooked meals</span>
                </li>
                <li className="flex items-start">
                  <div className="text-accent mr-2">•</div>
                  <span className="text-text-light">Medication management</span>
                </li>
                <li className="flex items-start">
                  <div className="text-accent mr-2">•</div>
                  <span className="text-text-light">Social activities</span>
                </li>
                <li className="flex items-start">
                  <div className="text-accent mr-2">•</div>
                  <span className="text-text-light">Transportation coordination</span>
                </li>
                <li className="flex items-start">
                  <div className="text-accent mr-2">•</div>
                  <span className="text-text-light">Laundry & housekeeping</span>
                </li>
                <li className="flex items-start">
                  <div className="text-accent mr-2">•</div>
                  <span className="text-text-light">Hospice coordination</span>
                </li>
                <li className="flex items-start">
                  <div className="text-accent mr-2">•</div>
                  <span className="text-text-light">Family support</span>
                </li>
              </ul>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm">
              <h3 className="text-xl font-bold mb-3">Facility Features:</h3>
              <ul className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <li className="flex items-start">
                  <div className="text-accent mr-2">•</div>
                  <span className="text-text-light">ADA-accessible design</span>
                </li>
                <li className="flex items-start">
                  <div className="text-accent mr-2">•</div>
                  <span className="text-text-light">Emergency call system</span>
                </li>
                <li className="flex items-start">
                  <div className="text-accent mr-2">•</div>
                  <span className="text-text-light">Comfortable common areas</span>
                </li>
                <li className="flex items-start">
                  <div className="text-accent mr-2">•</div>
                  <span className="text-text-light">Outdoor garden space</span>
                </li>
                <li className="flex items-start">
                  <div className="text-accent mr-2">•</div>
                  <span className="text-text-light">Private & semi-private rooms</span>
                </li>
                <li className="flex items-start">
                  <div className="text-accent mr-2">•</div>
                  <span className="text-text-light">Cable TV & Wi-Fi</span>
                </li>
                <li className="flex items-start">
                  <div className="text-accent mr-2">•</div>
                  <span className="text-text-light">Secure medication storage</span>
                </li>
                <li className="flex items-start">
                  <div className="text-accent mr-2">•</div>
                  <span className="text-text-light">Fire safety systems</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Location Section */}
      <section className="bg-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto flex flex-col md:flex-row items-center gap-10">
            <div className="w-full md:w-1/2">
              {/* Google Maps integration */}
              <div className="rounded-lg h-72 overflow-hidden">
                <GoogleMap address="17705 54th Avenue W, Lynnwood, WA 98037" height="100%" />
              </div>
            </div>
            <div className="w-full md:w-1/2">
              <h2 className="text-3xl font-bold mb-4">Our Location</h2>
              <p className="text-text-light mb-4">
                Our Adult Family House is located in a quiet residential neighborhood in Lynnwood, Washington, offering a peaceful setting while still being close to medical facilities, shopping, and community resources.
              </p>
              <div className="bg-tertiary/20 p-5 rounded-lg">
                <h3 className="font-bold text-lg mb-2 text-primary">Address:</h3>
                <address className="not-italic text-text-light mb-4">
                  17705 54th Avenue W<br />
                  Lynnwood, WA 98037
                </address>
                <div className="flex items-center text-text-light">
                  <Clock className="w-4 h-4 mr-2" />
                  <span>Visiting hours: 9:00 AM - 7:00 PM daily</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="bg-tertiary/10 py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold text-primary mb-4">Ready to Learn More?</h2>
          <p className="text-lg text-text-light mb-8 max-w-2xl mx-auto">
            We invite you to schedule a tour of our Adult Family House and discuss how we can meet your loved one's needs.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link to="/contact" className="btn-primary">
              Schedule a Tour
            </Link>
            <Link to="/services" className="btn-secondary">
              Explore Other Services
            </Link>
          </div>
        </div>
      </section>
    </>
  );
};

export default AdultFamilyHome;
